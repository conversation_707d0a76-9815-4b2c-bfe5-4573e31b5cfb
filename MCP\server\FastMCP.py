import time
import signal
import threading

class FastMCP:
    def __init__(self, name=None):
        self.name = name
        self.tools = {}
        self.resources = {}
        self.timeout = 25
        self._timer = None
        self._running = False

    def resource(self, pattern):
        def decorator(func):
            self.resources[pattern] = func
            return func
        return decorator

    def tool(self):
        def decorator(func):
            self.tools[func.__name__] = func
            return func
        return decorator

    def _handle_timeout(self):
        if self._running:
            # 添加超时日志记录
            print(f"[WARNING] Request timeout at {time.time()}")
            raise TimeoutError("Request processing timeout")

    def run(self):
        self._running = True
        while self._running:
            self._timer = threading.Timer(self.timeout, self._handle_timeout)
            self._timer.start()
            
            try:
                import json
                import sys
                import io
                
                # 设置标准流的编码
                sys.stdin = io.TextIOWrapper(sys.stdin.buffer, encoding='utf-8', errors='replace')
                
                while True:
                    line = sys.stdin.readline()
                    if not line:
                        break
                    
                    try:
                        request = json.loads(line)
                        if 'method' in request:
                            if request['method'] in self.tools:
                                result = self.tools[request['method']](*request.get('params', []))
                                response = {'jsonrpc': '2.0', 'result': result, 'id': request.get('id')}
                                print(json.dumps(response, ensure_ascii=False), flush=True)
                    except json.JSONDecodeError:
                        response = {'jsonrpc': '2.0', 'error': {'code': -32700, 'message': 'Parse error'}, 'id': None}
                        print(json.dumps(response, ensure_ascii=False), flush=True)
                    except Exception as e:
                        start_time = time.time()  # 定义start_time以避免引用错误
                        duration = time.time() - start_time
                        response = {'jsonrpc': '2.0', 'error': {'code': -32000, 'message': str(e)}, 'id': request.get('id')}
                        print(json.dumps(response, ensure_ascii=False), flush=True)
                        if duration > 1:  # 超过1秒的请求记录警告
                            print(f"WARNING: Request took {duration:.2f}s", file=sys.stderr)
                            
            except Exception as e:
                print(f"Error processing request: {e}")
                signal.alarm(0)  # 重置超时计时器
            except KeyboardInterrupt:
                pass
            finally:
                if self._timer:
                    self._timer.cancel()
