class FastMCP:
    def __init__(self, name=None):
        self.name = name
        self.tools = {}
        self.resources = {}
        self._running = False

    def resource(self, pattern):
        def decorator(func):
            self.resources[pattern] = func
            return func
        return decorator

    def tool(self):
        def decorator(func):
            self.tools[func.__name__] = func
            return func
        return decorator



    def run(self):
        self._running = True
        try:
            import json
            import sys
            import io

            # 设置标准流的编码
            sys.stdin = io.TextIOWrapper(sys.stdin.buffer, encoding='utf-8', errors='replace')

            while self._running:
                try:
                    line = sys.stdin.readline()
                    if not line:
                        break

                    line = line.strip()
                    if not line:
                        continue

                    try:
                        request = json.loads(line)
                        if 'method' in request:
                            method_name = request['method']
                            if method_name in self.tools:
                                result = self.tools[method_name](*request.get('params', []))
                                response = {'jsonrpc': '2.0', 'result': result, 'id': request.get('id')}
                                print(json.dumps(response, ensure_ascii=False), flush=True)
                            elif method_name in self.resources:
                                result = self.resources[method_name](*request.get('params', []))
                                response = {'jsonrpc': '2.0', 'result': result, 'id': request.get('id')}
                                print(json.dumps(response, ensure_ascii=False), flush=True)
                            else:
                                response = {'jsonrpc': '2.0', 'error': {'code': -32601, 'message': f'Method not found: {method_name}'}, 'id': request.get('id')}
                                print(json.dumps(response, ensure_ascii=False), flush=True)
                    except json.JSONDecodeError:
                        response = {'jsonrpc': '2.0', 'error': {'code': -32700, 'message': 'Parse error'}, 'id': None}
                        print(json.dumps(response, ensure_ascii=False), flush=True)
                    except Exception as e:
                        response = {'jsonrpc': '2.0', 'error': {'code': -32000, 'message': str(e)}, 'id': request.get('id', None)}
                        print(json.dumps(response, ensure_ascii=False), flush=True)

                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f"Error processing request: {e}", file=sys.stderr)

        except KeyboardInterrupt:
            pass
        finally:
            self._running = False
