# Crawl4AI API Token (optional)
# If set, this token will be used for authentication with the Crawl4AI service
# A default demo key is provided for testing, but you may want to change this for production
CRAWL4AI_API_TOKEN=devdocs-demo-key

# Resource limits for Crawl4AI container
# Adjust these values based on your system resources
MAX_CONCURRENT_TASKS=5

# MCP Host (used by containers to communicate with the MCP server on the host)
# By default, this is set to host.docker.internal in the docker-compose.yml file
# For macOS and Windows: Leave this commented out, it will work automatically
# For Linux: If you have connectivity issues, uncomment and set to your host's IP address:
# MCP_HOST=192.168.1.x  # Replace with your actual host IP

# Uncomment and set these if you want to use LLM features in Crawl4AI
# OPENAI_API_KEY=
# ANTHROPIC_API_KEY=