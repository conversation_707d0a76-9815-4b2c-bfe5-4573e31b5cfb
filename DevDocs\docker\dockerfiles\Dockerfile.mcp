FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# Copy MCP server code
COPY fast-markdown-mcp/ /app/

# Install dependencies directly without using a virtual environment
RUN pip install --no-cache-dir -e .

# Create necessary directories
RUN mkdir -p /app/storage/markdown

# Expose the port that the MCP server might use for communication
EXPOSE 8765

# Command to run the MCP server
CMD ["python", "-m", "fast_markdown_mcp.server", "/app/storage/markdown"]