import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";

// 初始化服务器实例
const transport = new StdioServerTransport();
const server = new McpServer({
    ...transport,
    name: "css-mcp-server",
    version: "1.0.0"
});

// 启动服务器
transport.start().then(() => {
    console.log("服务器已启动");
}).catch((error) => {
    console.error("服务器启动失败:", error);
    process.exit(1);
});