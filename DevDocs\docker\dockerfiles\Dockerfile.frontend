# Base Node.js image
FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package.json package-lock.json ./
RUN npm ci

# Set environment variables
ENV PORT 3001
ENV BACKEND_URL http://backend:24125
ENV NEXT_PUBLIC_BACKEND_URL http://backend:24125
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production

# Copy the rest of the application
COPY . .

# Create public directory if it doesn't exist
RUN mkdir -p public

# Next.js build
RUN npm run build

EXPOSE 3001

# Start the application
CMD ["npm", "start"]