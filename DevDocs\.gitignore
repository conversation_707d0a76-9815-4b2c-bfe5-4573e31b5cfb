# Dependencies
node_modules/
.pnp/
.pnp.js

# Testing
coverage/

# Next.js
.next/
out/
build/
dist/

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local env files
.env*.local
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
ENV/
env/
backend/venv/
.env/
.venv/
env.bak/
venv.bak/
*.egg-info/
dist/
build/
eggs/
parts/
bin/
var/
sdist/
develop-eggs/
.installed.cfg
lib64/
*.egg

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.pem
*.iml

# Vercel
.vercel

# Typescript
*.tsbuildinfo
next-env.d.ts

# Debug
.debug/

# Misc
.DS_Store
*.pem
.env.local
.env.development.local
.env.test.local
.env.production.local

# MCP Server specific
fast-markdown-mcp/venv/
fast-markdown-mcp/__pycache__/
fast-markdown-mcp/**/__pycache__/
fast-markdown-mcp/build/
fast-markdown-mcp/dist/
fast-markdown-mcp/*.egg-info/

# Service logs
logs/
*.log
