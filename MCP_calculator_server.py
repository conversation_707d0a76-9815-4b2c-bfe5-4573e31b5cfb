# 导入 FastMCP 类和相关模块
from MCP.server.FastMCP import FastMCP
import json
import os
import sys
import time
import threading

# 示例配置文件内容（注释形式）
# {
#     "name": "MyMCP",
#     "host": "0.0.0.0",
#     "port": 8080
# }

# 从环境变量或配置文件中加载配置
config_path = os.getenv('MCP_CONFIG_PATH', 'mcp_config.json')
try:
    with open(config_path) as f:
        config = json.load(f)
except (FileNotFoundError, json.JSONDecodeError):
    config = {"name": "Demo", "host": "localhost", "port": 8000}

MCP = FastMCP(config["name"])

@MCP.tool()
def add(a: int, b: int) -> int:
    return a + b

@MCP.tool()
def get_greeting(name: str) -> str:
    return f"Hello, {name}!"

# 路径检查和系统信息
if not os.path.exists(__file__):
    print(f"Error: File not found at {__file__}", file=sys.stderr)
    sys.exit(1)

print(f"Python executable: {sys.executable}")
print(f"Working directory: {os.getcwd()}")
print(f"Python version: {sys.version}")
print(f"Default encoding: {sys.getdefaultencoding()}")
print("服务器启动中...")

request_count = 0
request_lock = threading.Lock()

def monitor_requests():
    while True:
        with request_lock:
            print(f"Pending requests: {request_count}", file=sys.stderr)
        time.sleep(10)

threading.Thread(target=monitor_requests, daemon=True).start()

# 请求处理监控（已集成到 FastMCP 中）

class HeartbeatThread(threading.Thread):
    def __init__(self):
        super().__init__(daemon=True)
        self._running = True
    
    def run(self):
        while self._running:
            print("HEARTBEAT: Server is alive", file=sys.stderr)
            time.sleep(30)

# 在服务器启动代码后添加
heartbeat = HeartbeatThread()
heartbeat.start()

if __name__ == "__main__":
    print("启动 MCP Server...")
    try:
        MCP.run()  # FastMCP 使用 stdio 通信，不需要 host/port 参数
    except KeyboardInterrupt:
        print("服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {e}")
        sys.exit(1)