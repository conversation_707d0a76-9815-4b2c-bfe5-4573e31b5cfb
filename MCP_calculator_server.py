from MCP.server.FastMCP import FastMCP
import json
import os
import time
import threading

# 示例配置文件内容
{
    "name": "MyMCP",
    "host": "0.0.0.0", 
    "port": 8080
}

# 从环境变量或配置文件中加载配置
config_path = os.getenv('MCP_CONFIG_PATH', 'mcp_config.json')
try:
    with open(config_path) as f:
        config = json.load(f)
except (FileNotFoundError, json.JSONDecodeError):
    config = {"name": "Demo", "host": "localhost", "port": 8000}

MCP = FastMCP(config["name"])

@MCP.tool()
def add(a: int, b: int) -> int:
    return a + b

@MCP.resource("greeting://{name}")
def get_greeting(name: str) -> str:
    return f"Hello, {name}!"

# 在文件开头添加路径检查
import os
import sys

if not os.path.exists(__file__):
    print(f"Error: File not found at {__file__}", file=sys.stderr)
    sys.exit(1)

import sys
import os

print(f"Python executable: {sys.executable}")
print(f"Working directory: {os.getcwd()}")
print(f"Python version: {sys.version}")
print(f"Default encoding: {sys.getdefaultencoding()}")
print("服务器启动中...")
print(f"Python可执行文件路径: {sys.executable}")
print(f"工作目录: {os.getcwd()}")

import threading

request_count = 0
request_lock = threading.Lock()

def monitor_requests():
    while True:
        with request_lock:
            print(f"Pending requests: {request_count}", file=sys.stderr)
        time.sleep(10)

threading.Thread(target=monitor_requests, daemon=True).start()

def handle_request(self, request):
    start_time = time.time()
    try:
        # 处理请求逻辑
        result = self._process_request(request)
        return result
    except Exception as e:
        print(f"请求处理失败: {str(e)}")
        raise
    finally:
        duration = time.time() - start_time
        print(f"请求处理耗时: {duration:.2f}秒")

class HeartbeatThread(threading.Thread):
    def __init__(self):
        super().__init__(daemon=True)
        self._running = True
    
    def run(self):
        while self._running:
            print("HEARTBEAT: Server is alive", file=sys.stderr)
            time.sleep(30)

# 在服务器启动代码后添加
heartbeat = HeartbeatThread()
heartbeat.start()

if __name__ == "__main__":
    # 修改前
    # MCP.run(host=config["host"], port=config["port"])
    
    # 修改后
    # 删除或注释掉原来的host/port参数
    MCP.run()  # 直接调用不带参数