import json
import subprocess
import sys

def test_mcp_server():
    # Start the server in a separate process
    proc = subprocess.Popen(
        ['python', 'd:\\MCP_First\\MCP_calculator_server.py'],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        bufsize=1,
        universal_newlines=True
    )
    
    try:
        # Test add function
        request = {
            "jsonrpc": "2.0",
            "method": "add",
            "params": [2, 3],
            "id": 1
        }
        proc.stdin.write(json.dumps(request) + '\n')
        proc.stdin.flush()
        
        # Skip initial non-JSON output (like initialization messages)
        while True:
            line = proc.stdout.readline()
            if line.startswith('{'):
                try:
                    response = json.loads(line)
                    print("Response:", response)
                    break
                except json.JSONDecodeError:
                    continue
        
        # Test greeting resource
        request = {
            "jsonrpc": "2.0",
            "method": "get_greeting",
            "params": ["World"],
            "id": 2
        }
        proc.stdin.write(json.dumps(request) + '\n')
        proc.stdin.flush()
        
        response = proc.stdout.readline()
        print("Response:", response)
        
    finally:
        proc.terminate()

if __name__ == "__main__":
    test_mcp_server()