#!/usr/bin/env python3
"""
测试修复后的 MCP Server
"""
import json
import subprocess
import sys
import time

def test_python_mcp_server():
    """测试 Python MCP Server"""
    print("🧪 测试 Python MCP Server...")
    
    try:
        # 启动服务器
        proc = subprocess.Popen(
            [sys.executable, 'MCP_calculator_server.py'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        # 等待服务器启动
        time.sleep(2)
        
        # 测试加法工具
        request = {
            "jsonrpc": "2.0",
            "method": "add",
            "params": [10, 20],
            "id": 1
        }
        
        proc.stdin.write(json.dumps(request) + '\n')
        proc.stdin.flush()
        
        # 读取响应
        response_line = proc.stdout.readline()
        if response_line:
            try:
                response = json.loads(response_line)
                print(f"✅ 加法测试成功: {response}")
                
                if response.get('result') == 30:
                    print("✅ 计算结果正确!")
                else:
                    print(f"❌ 计算结果错误，期望 30，得到 {response.get('result')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析错误: {e}")
                print(f"原始响应: {response_line}")
        else:
            print("❌ 没有收到响应")
            
        # 测试问候资源
        request2 = {
            "jsonrpc": "2.0", 
            "method": "get_greeting",
            "params": ["Trae"],
            "id": 2
        }
        
        proc.stdin.write(json.dumps(request2) + '\n')
        proc.stdin.flush()
        
        response_line2 = proc.stdout.readline()
        if response_line2:
            try:
                response2 = json.loads(response_line2)
                print(f"✅ 问候测试成功: {response2}")
            except json.JSONDecodeError as e:
                print(f"❌ 问候测试 JSON 解析错误: {e}")
        
    except Exception as e:
        print(f"❌ Python MCP Server 测试失败: {e}")
    finally:
        if 'proc' in locals():
            proc.terminate()
            proc.wait()

def test_nodejs_mcp_server():
    """测试 Node.js MCP Server"""
    print("\n🧪 测试 Node.js MCP Server...")
    
    try:
        # 检查 Node.js 是否可用
        result = subprocess.run(['node', '--version'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Node.js 不可用")
            return
            
        print(f"✅ Node.js 版本: {result.stdout.strip()}")
        
        # 测试模块导入
        test_result = subprocess.run(['node', 'test.js'], 
                                   capture_output=True, text=True, timeout=10)
        
        if test_result.returncode == 0:
            print("✅ Node.js 模块导入测试成功")
            print(f"输出: {test_result.stdout}")
        else:
            print("❌ Node.js 模块导入测试失败")
            print(f"错误: {test_result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("❌ Node.js 测试超时")
    except Exception as e:
        print(f"❌ Node.js MCP Server 测试失败: {e}")

def main():
    print("🚀 开始测试修复后的 MCP Server 配置...\n")
    
    # 测试 Python 版本
    test_python_mcp_server()
    
    # 测试 Node.js 版本
    test_nodejs_mcp_server()
    
    print("\n📋 测试总结:")
    print("- Python MCP Server: 基于自定义 FastMCP 框架")
    print("- Node.js MCP Server: 基于官方 @modelcontextprotocol/sdk")
    print("- 配置文件: mcp_config.json (用于 Python 版本)")
    
    print("\n💡 使用建议:")
    print("1. Python 版本适合快速原型开发")
    print("2. Node.js 版本更符合官方标准")
    print("3. 根据您的需求选择合适的实现")

if __name__ == "__main__":
    main()
