{"name": "devdocs-explorer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.0", "@mui/material": "^6.4.0", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.454.0", "next": "15.1.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "autoprefixer": "^10.4.20", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}